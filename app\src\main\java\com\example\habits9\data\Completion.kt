package com.example.habits9.data

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Represents a habit completion record in the database.
 * This entity stores when a user completes a habit and the value for measurable habits.
 */
@Entity(
    tableName = "completions",
    foreignKeys = [
        ForeignKey(
            entity = Habit::class,
            parentColumns = ["id"],
            childColumns = ["habitId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["habitId"])]
)

data class Completion(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    /** The ID of the habit this completion belongs to */
    val habitId: Long,
    
    /** The date of completion as Unix timestamp (milliseconds) */
    val timestamp: Long,
    
    /** 
     * The value for measurable habits (e.g., "5", "1.5"). 
     * Should be null for Yes/No habits.
     */
    val value: String? = null
)