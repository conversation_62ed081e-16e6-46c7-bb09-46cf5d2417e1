# Project Roadmap: Habit Tracker v1.0

This document outlines the phased implementation plan for all remaining features for the first version of the application. The stages are organized to build foundational features first, ensuring a stable and logical development process.

### **Stage 1: Finalize Core Settings & Tracking**

This stage completes the most fundamental parts of the app's daily use and configuration.

- **Task 1: Implement "Start Day of Week" Setting**
    - **Goal:** Allow users to choose between Sunday or Monday as the first day of the week.
    - **Implementation:** Create a new "Settings" screen, save the user's choice to local storage (e.g., DataStore), and refactor all date calculation logic to respect this setting.
- **Task 2: Implement "Measurable" Habit Tracking**
    - **Goal:** Complete the habit tracking feature by enabling users to log numerical values for their measurable habits.
    - **Implementation:** Build the input dialog for measurable habits, allowing users to enter a number. This will complete the final phase of the "Daily Habit Tracking" plan.
    
- Task 3 : Introduce customizable Habit frequency
    
    ### **Phase 1: Database and Data Model Foundation**
    
    **Goal:** Modify the database to support complex recurrence rules. This is the bedrock of the entire feature.
    
    - **Task 1.1: Extend the `Habit` Data Model.**
        - In your `Habit.kt` file, add new columns to the `@Entity` to store the recurrence rules. The existing simple frequency column will be replaced by these:
            - `frequencyType`: A string or enum (e.g., "DAILY", "WEEKLY", "MONTHLY").
            - `repeatsEvery`: An integer (e.g., repeats every **2** weeks).
            - `daysOfWeek`: A list or set of integers/strings to store selections for weekly habits (e.g., `[1, 4, 6]` for Mon, Thu, Sat).
            - `dayOfMonth`: An integer for monthly habits (e.g., the **15**th).
            - `weekOfMonth`: An integer for advanced monthly habits (e.g., the **3rd** Tuesday).
    - **Task 1.2: Implement Database Migration.**
        - Create a Room database migration to safely add these new columns to the existing `habits` table without deleting user data.
    
    ---
    
    ### **Phase 2: Build the Frequency Selection UI**
    
    **Goal:** Create the new multi-screen UI that allows users to define a custom frequency.
    
    - **Task 2.1: Build the Main Frequency Screen.**
        - Create a new screen or dialog that shows the primary options: "Daily," "Weekly," "Monthly," "Yearly."
    - **Task 2.2: Build the Detailed "Weekly" Screen.**
        - Design the screen based on `31.jpg`.
        - It must include a number input for "Repeats every X weeks" and checkboxes for each day of the week (Sun-Sat).
    - **Task 2.3: Build the Detailed "Monthly" Screen.**
        - Design the screen based on `32.jpg`.
        - It must include options for "Day X of every Y months" and "The [first/second/third/fourth/last] [day of week] of every Y months."
    
    ---
    
    ### **Phase 3: Business Logic - Saving the Rules**
    
    **Goal:** Connect the new UI to the database and create the core scheduling logic.
    
    - **Task 3.1: Integrate the New UI Flow.**
        - In the "Create/Edit Habit" screen, replace the old frequency spinner with a button that launches the new selection flow from Phase 2.
    - **Task 3.2: Save Frequency Rules to Database.**
        - Implement the logic to take the user's selections from the UI and save them correctly into the new database columns created in Phase 1.
    - **Task 3.3: Create the Core Scheduling Function.**
        - This is the brain of the feature. Create a central, reusable function: `isHabitScheduled(habit: Habit, date: LocalDate): Boolean`.
        - This function will contain all the complex logic to read a habit's frequency rules and return `true` or `false` based on whether it's scheduled for the given date.
    
    ---
    
    ### **Phase 4: Update Home Screen with New Logic**
    
    **Goal:** Make the main tracking grid intelligent. It must now use the new scheduling logic to display habits correctly and calculate stats accurately.
    
    - **Task 4.1: Implement Smart Habit Display.**
        - In the `ViewModel` that prepares the data for the home screen, use the `isHabitScheduled()` function.
        - Only include habits in the list for a given day if `isHabitScheduled()` returns `true` for that day. This solves the show/hide requirement.
    - **Task 4.2: Implement Smart Cell Display.**
        - In the `HabitListView.kt` (or equivalent), for habits that are displayed, add a new layer of logic for the daily cells.
        - Disable and visually gray out any cell for a date where `isHabitScheduled()` returns `false`.
    - **Task 4.3: Implement Correct Streak Calculation.**
        - Rewrite the streak calculation logic. It must now iterate backward from "today," checking that the habit was both **scheduled** (`isHabitScheduled() == true`) and **completed** on each consecutive day. The streak breaks if a scheduled day was missed.
    - **Task 4.4: Implement Correct Weekly Percentage Calculation.**
        - Rewrite the "This Week's Percentage" logic.
        - Calculate `Total Opportunities` by counting how many days in the current week `isHabitScheduled()` returns `true` for that habit.
        - The final percentage will be `(Total Completions / Total Opportunities) * 100`.

---

### **Stage 2: Implement Cloud Sync with Firebase:**

- **Goal:** Replace the local Room database with Firestore.
- **Tasks:**
    1. Integrate Firebase into the project.
    2. Implement a user sign-in/sign-up screen.
    3. Rewrite our repository classes to save and fetch data from Firestore instead of Room.

---

### **Stage 3: Enhance the Homepage Experience**

With core tracking fully functional, this stage focuses on improving the usability and information density of the main screen.

- **Task 1: Display Daily Completion Percentages**
    - **Goal:** Show users their daily progress at a glance.
    - **Implementation:** Above each date column on the homepage grid, calculate and display the percentage of habits completed for that day.
- **Task 2: Implement Scrollable Dates**
    - **Goal:** Allow users to easily navigate through their habit history.
    - **Implementation:** Make the date header on the homepage horizontally scrollable, enabling users to swipe left and right to view past and future weeks.

---

### **Stage 4: Implement Habit Sorting**

This stage gives users powerful tools to organize their habit list according to their preferences.

- **Task 1: Sort by Properties**
    - **Goal:** Provide automated sorting options.
    - **Implementation:** Add a menu to the homepage that allows users to sort the habit list by **Name** (alphabetically), by **Color**, or grouped by **Section**.
- **Task 2: Implement Drag-and-Drop Reordering**
    - **Goal:** Allow for fully custom manual sorting.
    - **Implementation:** Add drag-and-drop functionality to the habit list on the homepage, allowing users to arrange their habits in any order they choose.

---

### **Stage 5: Build the Habit Metrics Screen**

This final stage delivers the advanced analytics feature, providing users with powerful insights into their long-term progress.

- **Task 1: Make Analytics Functional**
    - **Goal:** Transform the static "Habit Details" screen into a dynamic and data-rich analytics dashboard.
    - **Implementation:** Create the logic to calculate all necessary metrics (completion scores, streaks, history, etc.) and implement the charts and graphs to visualize this data for both individual habits and all habits combined.